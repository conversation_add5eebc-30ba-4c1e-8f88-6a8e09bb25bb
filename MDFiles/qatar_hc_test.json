{"exportMetadata": {"totalMessages": 1, "exportTimestamp": "2025-06-01T21:34:20", "processingTimestamp": "2025-06-01T21:34:20", "sourceFile": "qatar_hc_test.md", "parsingEngine": "HL7-V2 3.4.0", "dotNetVersion": ".NET 8.0"}, "messages": [{"messageMetadata": {"messageId": "MSG00001", "messageType": "ADT^A08", "triggerEvent": "A08", "sourceFile": "MDFiles/qatar_hc_test.md", "processingStatus": "Success", "validationErrors": [], "segmentCount": 3}, "mshSegment": {"fieldSeparator": "|", "encodingCharacters": "^~\\&", "sendingApplication": "SENDING_APP", "sendingFacility": "SENDING_FACILITY", "receivingApplication": "RECEIVING_APP", "receivingFacility": "RECEIVING_FACILITY", "dateTimeOfMessage": "20250601134258"}, "pidSegment": {"qatarIdExpiration": "QATAR_ID_EXP", "healthCardExpiration": "HC EXP DATE", "familyPhysician": "PHYSICIAN^FAMILY^DR^^^DR", "primaryOrganizationName": "PRIMARY"}}], "processingStatistics": {"totalProcessingTime": "00:00:00.000", "successfulMessages": 1, "failedMessages": 0}}