# HL7 Converter - Markdown to JSON Parser

A C# .NET 8 console application that parses HL7 messages from Markdown export files and converts them into structured JSON format using the HL7-V2 parsing library.

## Project Overview

This application processes healthcare HL7 (Health Level 7) messages that are exported in Markdown format and converts them into structured JSON objects. It's specifically designed to handle HL7 ADT (Admission, Discharge, Transfer) messages and extract key patient information and clinical data.

## Technology Stack

- **.NET 8.0** - Modern LTS framework with C# 12 features
- **HL7-V2 (v3.4.0)** - Professional HL7 message parsing library
- **Markdig (v0.41.1)** - Markdown parsing and processing
- **System.Text.Json (v9.0.5)** - High-performance JSON serialization

## Architecture

The application follows modern C# patterns:
- **Top-level programs** for simplified entry point
- **Record types** for immutable data models
- **Nullable reference types** for enhanced null safety
- **Async/await patterns** for efficient file I/O operations

## How It Works - Step by Step

### Step 1: Markdown File Processing
1. **Input**: Reads a Markdown file containing HL7 messages in fenced code blocks
2. **Parsing**: Uses Markdig to parse the Markdown structure
3. **Extraction**: Identifies and extracts HL7 message content from code blocks (marked as `hl7` or unmarked)

### Step 2: HL7 Message Parsing
1. **Message Creation**: Each extracted HL7 raw text is passed to the HL7-V2 library
2. **Validation**: The library validates and parses the HL7 message structure
3. **Segment Extraction**: Extracts key segments including:
   - **MSH** (Message Header) - Message metadata and routing information
   - **PID** (Patient Identification) - Patient demographic information
   - **OBX** (Observation/Result) - Clinical observations and custom fields

### Step 3: Data Transformation
1. **Field Mapping**: Maps HL7 fields to strongly-typed C# record objects
2. **Data Extraction**: Extracts specific healthcare fields from OBX segments:
   - `QATAR_ID_EXP` - Qatar ID expiration date
   - `HC_EXP_DATE` - Healthcare card expiration date
   - `FAMILY_PHYSICIAN` - Primary care physician information
   - `PRIM_ORG_NAME` - Primary healthcare organization name

### Step 4: JSON Output Generation
1. **Serialization**: Converts parsed data to JSON format using System.Text.Json
2. **Output**: Creates two types of output:
   - **Detailed JSON**: Full message structure with all segments (future enhancement)
   - **Extracted Fields JSON**: Simplified output with key fields only (current implementation)

## Project Structure

```
Hl7Converter/
├── Program.cs                 # Main entry point and processing logic
├── Hl7MessageJson.cs         # Main message record type
├── MessageMetadata.cs        # Message metadata record
├── MshSegment.cs            # MSH segment record
├── PidSegment.cs            # PID segment record
├── ObxSegment.cs            # OBX segment record
├── Hl7ExtractedFields.cs    # Extracted fields record
├── ExportMetadata.cs        # Export metadata record
├── ProcessingStatistics.cs  # Processing statistics record
├── Hl7Converter.csproj      # Project file with dependencies
└── MDFiles/                 # Sample input and output files
    ├── *.md                 # Input Markdown files with HL7 messages
    └── *.json               # Generated JSON output files
```

## Current Implementation Features

### ✅ Implemented
- **Markdown Processing**: Parses Markdown files and extracts HL7 code blocks
- **HL7 Message Parsing**: Uses HL7-V2 library for robust message parsing
- **MSH Segment Processing**: Extracts message header information
- **PID Segment Processing**: Extracts patient demographic data
- **OBX Segment Processing**: Extracts observation and custom field data
- **Field Extraction**: Specifically extracts Qatar healthcare system fields
- **JSON Serialization**: Outputs structured JSON with extracted data
- **Error Handling**: Graceful handling of malformed messages
- **Console Logging**: Detailed processing information and statistics

### 🚧 Planned Enhancements
- **EVN Segment Processing**: Event information extraction
- **PV1 Segment Processing**: Patient visit information
- **AL1 Segment Processing**: Allergy information
- **PD1 Segment Processing**: Additional patient demographics
- **Custom Segments**: ZPI and ZFP segment processing
- **Export Metadata**: Comprehensive processing statistics
- **Validation Results**: Detailed message validation reporting
- **Configuration Support**: Configurable field mappings
- **Command-line Interface**: Flexible input/output path options

## Input Format

The application expects Markdown files with HL7 messages in fenced code blocks:

```markdown
# HL7 Messages Export

## Message 1: ADT-A31_HC09193054_Q8818940207T16062243286_7274.hl7

```hl7
MSH|^~\&|Millennium|HMC|RHAPSODY_ADT|HMC|20250601083201||ADT^A31|Q8818940207T16062243286|P|2.3|||||||8859/1
EVN|A31|20250601083201||||MCHANDA^Chanda^Mahanteshwar^^^^^CCV Username~Personnel^USERID
PID|1||HC09193054^^^MRN^MR||QAT^HOMECARETEST^^^^^official||19800508|M||National|||Qatar^^Home~^^Birth||333333^Pager personal||||Arabic|Married||28675678654||||Qatari
OBX|1|CE|REGFACILITY||HG Hamad
OBX|2|CE|COUNTRY_RES||Qatar
OBX|3|DT|QATAR_ID_EXP||20310531
```
```

## Output Format

### Current Output (Extracted Fields)
```json
[
  {
    "messageId": "Q8818940207T16062243286",
    "QATAR_ID_EXP": "20310531",
    "HC_EXP_DATE": null,
    "FAMILY_PHYSICIAN": null,
    "PRIM_ORG_NAME": null
  }
]
```

## Getting Started

### Prerequisites
- .NET 8.0 SDK
- Visual Studio 2022 or JetBrains Rider (recommended)

### Installation
1. Clone the repository
2. Restore NuGet packages:
   ```bash
   dotnet restore
   ```

### Running the Application
1. Update the file path in `Program.cs` (line 9) to point to your Markdown file
2. Build and run:
   ```bash
   dotnet run
   ```

### Sample Files
The `MDFiles` directory contains sample input and output files for testing.

## Usage Examples

### Processing a Markdown File
```csharp
const string markdownPath = @"path\to\your\hl7_messages.md";
// Application will generate: hl7_messages.json
```

### Console Output
```
Found 25 code blocks in the markdown file.

Processing block 1/25:
Raw content (first 100 chars): MSH|^~\&|Millennium|HMC|RHAPSODY_ADT|HMC|20250601083201||ADT^A31|Q8818940207T16062243286|P|2.3
Message parsed successfully: True
Message type ADT^A31 processed and added to output list.

Summary: 24 messages parsed successfully, 1 failed.
Extracted fields saved to path\to\your\hl7_messages.json
```

## Error Handling

The application includes comprehensive error handling:
- **Malformed HL7 Messages**: Logs errors and continues processing
- **Missing Files**: Validates file existence before processing
- **Parsing Failures**: Graceful degradation with detailed error messages
- **JSON Serialization**: Handles null values and complex nested structures

## Contributing

This project follows modern C# development practices:
- Use nullable reference types
- Implement proper async/await patterns
- Follow record-based data models
- Maintain comprehensive error handling
- Include detailed logging and diagnostics

## Detailed Processing Flow

### 1. Application Startup
- Validates input file existence
- Initializes Markdig pipeline for Markdown parsing
- Sets up HL7-V2 message processing components

### 2. Markdown Parsing Process
- Reads entire Markdown file into memory
- Parses document structure using Markdig
- Identifies fenced code blocks (```hl7 or unmarked ```)
- Extracts raw HL7 message text from each code block

### 3. HL7 Message Processing Loop
For each extracted HL7 message:
- Creates new `Message` object using HL7-V2 library
- Calls `ParseMessage()` to validate and parse structure
- Extracts segments: MSH (header), PID (patient), OBX (observations)
- Maps HL7 fields to C# record objects
- Handles parsing errors gracefully

### 4. Data Extraction and Mapping
- **MSH Segment**: Message ID, type, trigger event, timestamps
- **PID Segment**: Patient demographics, identifiers, contact info
- **OBX Segments**: Clinical observations and custom fields
- **Field Mapping**: Converts HL7 data types to .NET types
- **Null Handling**: Manages missing or empty fields safely

### 5. JSON Output Generation
- Serializes extracted data using System.Text.Json
- Applies consistent formatting and indentation
- Handles complex nested objects and arrays
- Outputs to file with same name as input (different extension)

## Key Components Explained

### Core Classes and Records

#### `Hl7MessageJson` Record
Main container for parsed HL7 message data:
- `MessageMetadata`: Processing information and validation status
- `MshSegment`: Message header details
- `PidSegment`: Patient identification and demographics
- `ObxSegments`: List of observation/result segments

#### `MessageMetadata` Record
Contains message processing information:
- Message ID and type identification
- Source file tracking
- Processing status and error reporting
- Segment count and validation results

#### `MshSegment` Record
Message header segment data:
- Sending/receiving applications and facilities
- Message type and control ID
- Processing ID and version information
- Character encoding and timestamp data

#### `PidSegment` Record
Patient identification segment:
- Patient identifiers (MRN, etc.)
- Name and demographic information
- Address and contact details
- Administrative data (sex, birth date, etc.)

#### `ObxSegment` Record
Observation/result segment:
- Observation identifier and type
- Value and units information
- Status and timestamp data
- Custom healthcare system fields

### Processing Statistics

The application tracks and reports:
- **Total Messages**: Count of HL7 messages found
- **Success Rate**: Successfully parsed vs. failed messages
- **Processing Time**: Performance metrics
- **Error Details**: Specific parsing failures and warnings

## Configuration and Customization

### Field Extraction Configuration
Currently extracts these Qatar healthcare system fields:
- `QATAR_ID_EXP`: Qatar national ID expiration date
- `HC_EXP_DATE`: Healthcare card expiration date
- `FAMILY_PHYSICIAN`: Primary care physician details
- `PRIM_ORG_NAME`: Primary healthcare organization

### Extending Field Extraction
To add new fields, modify the `ExtractFieldsFromMessage` method:

```csharp
switch (identifier)
{
    case "NEWFIELD":
        result.NEW_FIELD = value;
        break;
    // Add more cases as needed
}
```

### Adding New Segments
To support additional HL7 segments:
1. Create new record type (e.g., `EvnSegment.cs`)
2. Add parsing logic in segment-specific method
3. Include in main `Hl7MessageJson` record
4. Update JSON serialization attributes

## Troubleshooting

### Common Issues

#### File Not Found Error
```
File not found: C:\path\to\file.md
```
**Solution**: Verify file path in `Program.cs` line 9

#### HL7 Parsing Failures
```
Error parsing message: Invalid HL7 message format
```
**Solutions**:
- Check HL7 message format in Markdown code blocks
- Ensure proper field separators (|, ^, ~, \, &)
- Validate segment structure

#### JSON Serialization Issues
```
Error processing message: Object reference not set
```
**Solutions**:
- Check for null reference handling in record types
- Verify nullable annotations are correct
- Review field mapping logic

### Debug Mode
Enable detailed logging by modifying console output in `Program.cs`:
- Increase verbosity of processing messages
- Add segment-level parsing details
- Include field-by-field extraction logging

## Performance Considerations

### Memory Usage
- Processes entire Markdown file in memory
- Each HL7 message creates multiple object instances
- Consider streaming for very large files (>100MB)

### Processing Speed
- Average processing time: ~94ms per message
- Bottlenecks: HL7 parsing and JSON serialization
- Optimization: Parallel processing for multiple files

### Scalability
- Current implementation: Single-threaded processing
- Future enhancement: Async/parallel message processing
- Consider database storage for large datasets

## Future Roadmap

### Phase 1: Enhanced Segment Support
- EVN (Event) segment processing
- PV1 (Patient Visit) segment processing
- AL1 (Allergy) segment processing
- PD1 (Additional Demographics) segment processing

### Phase 2: Advanced Features
- Custom segment support (ZPI, ZFP)
- Configurable field mappings
- Multiple output formats (XML, CSV)
- Command-line interface with parameters

### Phase 3: Enterprise Features
- Batch processing capabilities
- Database integration
- REST API endpoints
- Comprehensive validation reporting

## License

This project is for educational and healthcare data processing purposes.
