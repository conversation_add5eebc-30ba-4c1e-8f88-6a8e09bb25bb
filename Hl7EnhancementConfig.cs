namespace Hl7Converter;

/// <summary>
/// Configuration settings for HL7 message enhancement functionality
/// </summary>
public class Hl7EnhancementConfig
{
    /// <summary>
    /// Path to store original HL7 messages
    /// </summary>
    public string OriginalMessagesPath { get; set; } = "hl7-messages/original";

    /// <summary>
    /// Path to store enhanced HL7 messages
    /// </summary>
    public string EnhancedMessagesPath { get; set; } = "hl7-messages/enhanced";

    /// <summary>
    /// Azure Blob container name for HL7 messages
    /// </summary>
    public string BlobContainerName { get; set; } = "hl7-messages";

    /// <summary>
    /// Enable database lookup for patient data enhancement
    /// </summary>
    public bool EnableDatabaseLookup { get; set; } = true;

    /// <summary>
    /// List of required OBX fields that should be present in enhanced messages
    /// </summary>
    public List<string> RequiredFields { get; set; } = new()
    {
        "QATAR_ID_EXP",
        "HC_EXP_DATE", 
        "FAMILY_PHYSICIAN",
        "PRIM_ORG_NAME"
    };
}
