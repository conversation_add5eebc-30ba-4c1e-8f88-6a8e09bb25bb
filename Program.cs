using System.Text.Json;
using Azure.Storage.Blobs;
using Efferent.HL7.V2;
using Hl7Converter;
using Hl7Converter.Context;
using Hl7Converter.Services;
using Markdig;
using Markdig.Syntax;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

// HL7 Message Enhancement Console Application
// Implements Azure Blob trigger simulation with database enhancement

Console.WriteLine("=== HL7 Message Enhancement System ===");
Console.WriteLine("Starting application...\n");

// Build configuration
var configuration = new ConfigurationBuilder()
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .Build();

// Setup dependency injection
var services = new ServiceCollection();
ConfigureServices(services, configuration);
var serviceProvider = services.BuildServiceProvider();

// Get logger
var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
logger.LogInformation("HL7 Enhancement System started");

try
{
    // Get main service
    var enhancementService = serviceProvider.GetRequiredService<Hl7EnhancementService>();
    var blobSimulator = serviceProvider.GetRequiredService<BlobTriggerSimulator>();

    // Check database connectivity
    var patientService = serviceProvider.GetRequiredService<PatientDataService>();
    var isDatabaseAvailable = await patientService.IsDatabaseAvailableAsync();

    if (isDatabaseAvailable)
    {
        logger.LogInformation("Database connection successful");
    }
    else
    {
        logger.LogWarning("Database connection failed - will process messages without enhancement");
    }

    // Process mode selection
    Console.WriteLine("Select processing mode:");
    Console.WriteLine("1. Process from Azure Blob Storage");
    Console.WriteLine("2. Process from local directory");
    Console.WriteLine("3. Process from Markdown file (legacy mode)");
    Console.Write("Enter your choice (1-3): ");

    var choice = Console.ReadLine();

    switch (choice)
    {
        case "1":
            await ProcessFromBlobStorageAsync(enhancementService, blobSimulator, logger);
            break;
        case "2":
            await ProcessFromLocalDirectoryAsync(enhancementService, blobSimulator, logger);
            break;
        case "3":
            await ProcessFromMarkdownFileAsync(enhancementService, logger);
            break;
        default:
            Console.WriteLine("Invalid choice. Defaulting to local directory processing.");
            await ProcessFromLocalDirectoryAsync(enhancementService, blobSimulator, logger);
            break;
    }
}
catch (Exception ex)
{
    logger.LogError(ex, "Fatal error in application");
    Console.WriteLine($"Fatal error: {ex.Message}");
}
finally
{
    serviceProvider.Dispose();
    Console.WriteLine("\nPress any key to exit...");
    Console.ReadKey();
}

// --- Helper Methods ---
static Hl7ExtractedFields ExtractFieldsFromMessage(Message message)
{
    var result = new Hl7ExtractedFields();
    var obxSegments = message.Segments("OBX");
    for (var i = 0; i < obxSegments.Count; i++)
    {
        var identifier = (message.GetValue($"OBX({i+1}).3") ?? "").Replace(" ", "").Replace("_", "").ToUpperInvariant();
        var value = message.GetValue($"OBX({i+1}).5");
        switch (identifier)
        {
            case "QATARIDEXP":
                result.QATAR_ID_EXP = value;
                break;
            case "HCEXPDATE":
                result.HC_EXP_DATE = value;
                break;
            case "FAMILYPHYSICIAN":
                result.FAMILY_PHYSICIAN = value;
                break;
            case "PRIMORGNAME":
                result.PRIM_ORG_NAME = value;
                break;
        }
    }
    return result;
}

static void ProcessCodeBlock(FencedCodeBlock block, int blockIndex, int totalBlocks, string markdownPath, List<Hl7MessageJson> messages, ref int successCount, ref int failureCount)
{
    var hl7Raw = block.Lines.ToString();
    Console.WriteLine($"\nProcessing block {blockIndex}/{totalBlocks}:");
    try
    {
        Console.WriteLine($"Raw content (first 100 chars): {GetFirstChars(hl7Raw, 100)}");
        var message = ParseMessageWithLogging(hl7Raw, out var isParsed);
        Console.WriteLine($"Message parsed successfully: {isParsed}");
        if (isParsed)
        {
            if (TryProcessMessage(message, markdownPath, messages, out var messageType))
            {
                successCount++;
                Console.WriteLine($"Message type {messageType} processed and added to output list.");
            }
            else
            {
                failureCount++;
            }
        }
        else
        {
            failureCount++;
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Error parsing message: {ex.Message} {(ex.InnerException != null ? $"Error: {ex.InnerException.Message}" : "")}");
        failureCount++;
    }
}

static string GetFirstChars(string input, int count) => input.Length > count ? input[..count] : input;

static Message ParseMessageWithLogging(string hl7Raw, out bool isParsed)
{
    var message = new Message(hl7Raw);
    isParsed = message.ParseMessage();
    return message;
}

static bool TryProcessMessage(Message message, string markdownPath, List<Hl7MessageJson> messages, out string messageType)
{
    messageType = string.Empty;
    try
    {
        messageType = message.GetValue("MSH.9");
        var msg = Hl7MessageJson.FromHl7V2(message, markdownPath);
        if (msg != null)
            messages.Add(msg);
        return true;
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Error processing message: {ex.Message} {(ex.InnerException != null ? $"Error: {ex.InnerException.Message}" : "")}");
        return false;
    }
}