using System.Text.Json.Serialization;
using Efferent.HL7.V2;

namespace Hl7Converter;

public record ObxSegment
{
    [JsonPropertyName("setId")] public string? SetId { get; set; }
    [JsonPropertyName("valueType")] public string? ValueType { get; set; }
    [JsonPropertyName("observationIdentifier")] public string? ObservationIdentifier { get; set; }
    [JsonPropertyName("observationIdentifierText")] public string? ObservationIdentifierText { get; set; }
    [JsonPropertyName("observationValue")] public string? ObservationValue { get; set; }
    [JsonPropertyName("units")] public string? Units { get; set; }
    [JsonPropertyName("referenceRange")] public string? ReferenceRange { get; set; }
    [JsonPropertyName("abnormalFlags")] public string? AbnormalFlags { get; set; }
    [JsonPropertyName("observationDateTime")] public string? ObservationDateTime { get; set; }
    
    public static ObxSegment FromHl7V2(Message? message, Segment? obx)
    {
        if (obx == null) return new ObxSegment();
        
        // Get the index of this OBX segment
        int index = 0;
        var allObx = message?.Segments("OBX");
        if (allObx != null)
            for (int i = 0; i < allObx.Count; i++)
            {
                if (allObx[i] != obx) continue;
                index = i + 1; // 1-based index for HL7
                break;
            }

        // Helper function to safely get field values
        string? GetFieldValueSafely(string field)
        {
            try
            {
                return message?.GetValue(field);
            }
            catch
            {
                return string.Empty;
            }
        }
        
        // Extract OBX identifier parts (OBX-3 often has format: code^text^coding system)
        string? observationId = GetFieldValueSafely($"OBX({index}).3");
        string? observationIdText = "";
        
        string?[] idParts = observationId?.Split('^')!;
        if (idParts.Length <= 1)
            return new ObxSegment
            {
                SetId = GetFieldValueSafely($"OBX({index}).1"),
                ValueType = GetFieldValueSafely($"OBX({index}).2"),
                ObservationIdentifier = observationId,
                ObservationIdentifierText = observationIdText,
                ObservationValue = GetFieldValueSafely($"OBX({index}).5"),
                Units = GetFieldValueSafely($"OBX({index}).6"),
                ReferenceRange = GetFieldValueSafely($"OBX({index}).7"),
                AbnormalFlags = GetFieldValueSafely($"OBX({index}).8"),
                ObservationDateTime = GetFieldValueSafely($"OBX({index}).14")
            };
        
        observationId = idParts[0];
        observationIdText = idParts[1];

        return new ObxSegment
        {
            SetId = GetFieldValueSafely($"OBX({index}).1"),
            ValueType = GetFieldValueSafely($"OBX({index}).2"),
            ObservationIdentifier = observationId,
            ObservationIdentifierText = observationIdText,
            ObservationValue = GetFieldValueSafely($"OBX({index}).5"),
            Units = GetFieldValueSafely($"OBX({index}).6"),
            ReferenceRange = GetFieldValueSafely($"OBX({index}).7"),
            AbnormalFlags = GetFieldValueSafely($"OBX({index}).8"),
            ObservationDateTime = GetFieldValueSafely($"OBX({index}).14")
        };
    }
}