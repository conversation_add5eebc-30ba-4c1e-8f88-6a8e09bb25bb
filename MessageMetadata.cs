using System.Text.Json.Serialization;

namespace Hl7Converter;

public record MessageMetadata
{
    [JsonPropertyName("messageId")] public string? MessageId { get; set; }
    [JsonPropertyName("messageType")] public string? MessageType { get; set; }
    [JsonPropertyName("triggerEvent")] public string? TriggerEvent { get; set; }
    [JsonPropertyName("sourceFile")] public string? SourceFile { get; set; }
    [JsonPropertyName("lastModified")] public string? LastModified { get; set; }
    [JsonPropertyName("processingStatus")] public string? ProcessingStatus { get; set; }
    [JsonPropertyName("validationErrors")] public List<string>? ValidationErrors { get; set; }
    [JsonPropertyName("segmentCount")] public int SegmentCount { get; set; }
}