using System.Text.Json.Serialization;
using Efferent.HL7.V2;

namespace Hl7Converter;

public record PidSegment
{
    [JsonPropertyName("qatarIdExpiration")] public string? QatarIdExpiration { get; set; }
    [JsonPropertyName("healthCardExpiration")] public string? HealthCardExpiration { get; set; }
    [JsonPropertyName("familyPhysician")] public string? FamilyPhysician { get; set; }
    [JsonPropertyName("primaryOrganizationName")] public string? PrimaryOrganizationName { get; set; }
    
    public static PidSegment FromHl7V2(Message? message)
    {
        if (message == null) 
            return new PidSegment();
        
        var result = new PidSegment();
        ProcessPidSegment(message, result);
        ProcessRolSegments(message, result);
        
        return result;
    }

    private static void ProcessPidSegment(Message message, PidSegment result)
    {
        try
        {
            var pidSegment = message.DefaultSegment("PID");
            if (pidSegment == null) 
                return;

            var (qatarIdExp, healthCardExp) = ExtractExpirationDates(message);
            result.QatarIdExpiration = qatarIdExp;
            result.HealthCardExpiration = healthCardExp;
        }
        catch
        {
            // Continue processing even if PID segment fails
        }
    }

    private static (string qatarIdExp, string healthCardExp) ExtractExpirationDates(Message message)
    {
        string qatarIdExp = "";
        string healthCardExp = "";
        const int maxRepeats = 10;
        
        for (int i = 1; i <= maxRepeats; i++)
        {
            try
            {
                var idInfo = GetIdInformation(message, i);
                
                if (IsQatarId(idInfo))
                {
                    qatarIdExp = idInfo.ExpirationDate;
                }
                else if (IsHealthCard(idInfo))
                {
                    healthCardExp = idInfo.ExpirationDate;
                }
            }
            catch
            {
                // Continue to next repeat if there's an error
            }
        }
        
        return (qatarIdExp, healthCardExp);
    }

    private static IdInformation GetIdInformation(Message message, int repeatIndex)
    {
        return new IdInformation
        {
            IdValue = GetFieldValueSafely(message, $"PID.3({repeatIndex}).1"),
            IdType = GetFieldValueSafely(message, $"PID.3({repeatIndex}).5"),
            ExpirationDate = GetFieldValueSafely(message, $"PID.3({repeatIndex}).8"),
            Component7 = GetFieldValueSafely(message, $"PID.3({repeatIndex}).7")
        };
    }

    private static bool IsQatarId(IdInformation idInfo)
    {
        return idInfo.IdValue.StartsWith("QID") ||
               (idInfo.IdType != "" && idInfo.IdType.Contains("QATAR")) ||
               (idInfo.Component7 != "" && idInfo.Component7.Contains("QATAR_ID_EXP"));
    }

    private static bool IsHealthCard(IdInformation idInfo)
    {
        return idInfo.IdValue.StartsWith("HC") ||
               (idInfo.IdType != "" && idInfo.IdType.Contains("HC")) ||
               (idInfo.Component7 != "" && idInfo.Component7.Contains("HC EXP DATE"));
    }

    private static void ProcessRolSegments(Message message, PidSegment result)
    {
        try
        {
            var rolSegments = message.Segments("ROL");
            
            for (int i = 0; i < rolSegments.Count; i++)
            {
                ProcessSingleRolSegment(message, i + 1, result);
            }
        }
        catch
        {
            // Continue processing even if ROL segments fail
        }
    }

    private static void ProcessSingleRolSegment(Message message, int index, PidSegment result)
    {
        try
        {
            string roleType = GetFieldValueSafely(message, $"ROL({index}).3");
            
            if (IsFamilyPhysicianRole(roleType))
            {
                ExtractPhysicianInfo(message, index, result);
                ExtractOrganizationInfo(message, index, result);
            }
        }
        catch
        {
            // Continue to next ROL segment if there's an error
        }
    }

    private static bool IsFamilyPhysicianRole(string roleType)
    {
        return roleType.Contains("FAMILY") || 
               roleType.Contains("PRIMARY") || 
               roleType.Contains("ATTENDING") || 
               roleType.Contains("PHYSICIAN");
    }

    private static void ExtractPhysicianInfo(Message message, int index, PidSegment result)
    {
        string physicianName = GetFieldValueSafely(message, $"ROL({index}).4");
        if (!string.IsNullOrEmpty(physicianName))
        {
            result.FamilyPhysician = physicianName;
        }
    }

    private static void ExtractOrganizationInfo(Message message, int index, PidSegment result)
    {
        string orgField = GetFieldValueSafely(message, $"ROL({index}).11");
        
        if (string.IsNullOrEmpty(orgField) || !orgField.Contains("PRIM_ORG_NAME"))
            return;

        var orgParts = orgField.Split('^');
        result.PrimaryOrganizationName = orgParts.Length > 1 ? orgParts[1] : orgParts[0];
    }

    private static string GetFieldValueSafely(Message message, string field)
    {
        try
        {
            return message.GetValue(field);
        }
        catch
        {
            // Return empty string if field extraction fails
        }
        return string.Empty;
    }

    private sealed record IdInformation
    {
        public string IdValue { get; init; } = "";
        public string IdType { get; init; } = "";
        public string ExpirationDate { get; init; } = "";
        public string Component7 { get; init; } = "";
    }
}