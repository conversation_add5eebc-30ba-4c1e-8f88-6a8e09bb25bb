using System.Text.Json.Serialization;
using Efferent.HL7.V2;

namespace Hl7Converter;

public record Hl7MessageJson
{
    [JsonPropertyName("messageMetadata")] public MessageMetadata? MessageMetadata { get; set; }
    [JsonPropertyName("mshSegment")] public MshSegment? MshSegment { get; set; }
    [JsonPropertyName("pidSegment")] public PidSegment? PidSegment { get; set; }
    [JsonPropertyName("obxSegments")] public List<ObxSegment>? ObxSegments { get; set; }

    public static Hl7MessageJson? FromHl7V2(Message? message, string sourceFile)
    {
        // Access segment fields using HL7-V2 API
        var msh = message?.DefaultSegment("MSH");
        if (message != null)
        {
            int segmentCount = message.Segments().Count;

            string messageId = "";
            string messageType = "";
            string triggerEvent = "";

            if (msh != null)
            {
                // Get field values from MSH segment
                messageId = message.GetValue("MSH.10");
                messageType = message.GetValue("MSH.9");

                // Extract trigger event from message type (format: MessageType^TriggerEvent)
                var parts = messageType.Split('^');
                if (parts.Length > 1)
                {
                    triggerEvent = parts[1];
                }
            }

            // Only process OBX segments if this is an SIU message
            List<ObxSegment>? obxSegments = null;
            if (!messageType.StartsWith("SIU"))
                return new Hl7MessageJson
                {
                    MessageMetadata = new MessageMetadata
                    {
                        MessageId = messageId,
                        MessageType = messageType,
                        TriggerEvent = triggerEvent,
                        SourceFile = sourceFile,
                        LastModified = null,
                        ProcessingStatus = "Success",
                        ValidationErrors = [],
                        SegmentCount = segmentCount
                    },
                    MshSegment = MshSegment.FromHl7V2(message),
                    PidSegment = PidSegment.FromHl7V2(message),
                    ObxSegments = obxSegments
                };
            
            // Get all OBX segments
            var obxSegmentsList = message.Segments("OBX");
            if (obxSegmentsList.Count != 0)
            {
                obxSegments = obxSegmentsList.Select(obx => ObxSegment.FromHl7V2(message, obx)).ToList();
            }

            return new Hl7MessageJson
            {
                MessageMetadata = new MessageMetadata
                {
                    MessageId = messageId,
                    MessageType = messageType,
                    TriggerEvent = triggerEvent,
                    SourceFile = sourceFile,
                    LastModified = null,
                    ProcessingStatus = "Success",
                    ValidationErrors = [],
                    SegmentCount = segmentCount
                },
                MshSegment = MshSegment.FromHl7V2(message),
                PidSegment = PidSegment.FromHl7V2(message),
                ObxSegments = obxSegments
            };
        }

        // Add a return statement for the case when message is null
        return null;
    }
}