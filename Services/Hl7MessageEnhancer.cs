using Efferent.HL7.V2;
using Hl7Converter.Context;
using Microsoft.Extensions.Logging;
using System.Text;

namespace Hl7Converter.Services;

/// <summary>
/// Service for enhancing HL7 messages with missing OBX segments
/// </summary>
public class Hl7MessageEnhancer
{
    private readonly ILogger<Hl7MessageEnhancer> _logger;

    public Hl7MessageEnhancer(ILogger<Hl7MessageEnhancer> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Enhances HL7 message by adding missing OBX segments from patient data
    /// </summary>
    /// <param name="originalHl7">Original HL7 message content</param>
    /// <param name="patient">Patient data from database</param>
    /// <param name="extractedFields">Currently extracted fields from message</param>
    /// <returns>Enhanced HL7 message content</returns>
    public string EnhanceMessage(string originalHl7, Patient patient, Hl7ExtractedFields extractedFields)
    {
        try
        {
            _logger.LogInformation("Starting HL7 message enhancement");

            var lines = originalHl7.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries).ToList();
            var enhancedLines = new List<string>(lines);

            // Find the last OBX segment index to insert new segments after
            var lastObxIndex = FindLastObxSegmentIndex(enhancedLines);
            var insertIndex = lastObxIndex + 1;

            // Get next OBX sequence number
            var nextObxSequence = GetNextObxSequenceNumber(enhancedLines);

            // Add missing OBX segments
            var addedSegments = 0;

            // Add QATAR_ID_EXP if missing
            if (string.IsNullOrEmpty(extractedFields.QATAR_ID_EXP) && patient.QidExpiryDate.HasValue)
            {
                var qatarIdExpSegment = CreateObxSegment(nextObxSequence + addedSegments, "DT", "QATAR_ID_EXP", 
                    patient.QidExpiryDate.Value.ToString("yyyyMMdd"));
                enhancedLines.Insert(insertIndex + addedSegments, qatarIdExpSegment);
                addedSegments++;
                _logger.LogInformation("Added QATAR_ID_EXP OBX segment: {Date}", patient.QidExpiryDate.Value.ToString("yyyyMMdd"));
            }

            // Add HC_EXP_DATE if missing
            if (string.IsNullOrEmpty(extractedFields.HC_EXP_DATE) && patient.HcExpiryDate.HasValue)
            {
                var hcExpDateSegment = CreateObxSegment(nextObxSequence + addedSegments, "DT", "HC_EXP_DATE", 
                    patient.HcExpiryDate.Value.ToString("yyyyMMdd"));
                enhancedLines.Insert(insertIndex + addedSegments, hcExpDateSegment);
                addedSegments++;
                _logger.LogInformation("Added HC_EXP_DATE OBX segment: {Date}", patient.HcExpiryDate.Value.ToString("yyyyMMdd"));
            }

            // Add FAMILY_PHYSICIAN if missing
            if (string.IsNullOrEmpty(extractedFields.FAMILY_PHYSICIAN) && !string.IsNullOrEmpty(patient.AssignedFamilyPhysicianId))
            {
                var familyPhysicianSegment = CreateObxSegment(nextObxSequence + addedSegments, "CD", "FAMILY_PHYSICIAN", 
                    patient.AssignedFamilyPhysicianId);
                enhancedLines.Insert(insertIndex + addedSegments, familyPhysicianSegment);
                addedSegments++;
                _logger.LogInformation("Added FAMILY_PHYSICIAN OBX segment: {PhysicianId}", patient.AssignedFamilyPhysicianId);
            }

            // Add PRIM_ORG_NAME if missing
            if (string.IsNullOrEmpty(extractedFields.PRIM_ORG_NAME) && !string.IsNullOrEmpty(patient.AssignedHcCode))
            {
                var primOrgNameSegment = CreateObxSegment(nextObxSequence + addedSegments, "TX", "PRIM_ORG_NAME", 
                    patient.AssignedHcCode);
                enhancedLines.Insert(insertIndex + addedSegments, primOrgNameSegment);
                addedSegments++;
                _logger.LogInformation("Added PRIM_ORG_NAME OBX segment: {OrgName}", patient.AssignedHcCode);
            }

            var enhancedMessage = string.Join("\r\n", enhancedLines);
            
            _logger.LogInformation("HL7 message enhancement completed. Added {Count} OBX segments", addedSegments);
            
            return enhancedMessage;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enhancing HL7 message");
            return originalHl7; // Return original message on error
        }
    }

    /// <summary>
    /// Creates an OBX segment with the specified parameters
    /// </summary>
    private string CreateObxSegment(int sequenceNumber, string valueType, string identifier, string value)
    {
        return $"OBX|{sequenceNumber}|{valueType}|{identifier}||{value}||||||";
    }

    /// <summary>
    /// Finds the index of the last OBX segment in the message
    /// </summary>
    private int FindLastObxSegmentIndex(List<string> lines)
    {
        for (int i = lines.Count - 1; i >= 0; i--)
        {
            if (lines[i].StartsWith("OBX|"))
            {
                return i;
            }
        }
        
        // If no OBX segments found, find PID segment to insert after
        for (int i = 0; i < lines.Count; i++)
        {
            if (lines[i].StartsWith("PID|"))
            {
                return i;
            }
        }
        
        // Default to after MSH segment
        return 0;
    }

    /// <summary>
    /// Gets the next OBX sequence number
    /// </summary>
    private int GetNextObxSequenceNumber(List<string> lines)
    {
        var maxSequence = 0;
        
        foreach (var line in lines)
        {
            if (line.StartsWith("OBX|"))
            {
                var parts = line.Split('|');
                if (parts.Length > 1 && int.TryParse(parts[1], out var sequence))
                {
                    maxSequence = Math.Max(maxSequence, sequence);
                }
            }
        }
        
        return maxSequence + 1;
    }
}
