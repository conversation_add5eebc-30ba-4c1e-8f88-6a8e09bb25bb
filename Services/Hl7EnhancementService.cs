using Efferent.HL7.V2;
using Microsoft.Extensions.Logging;

namespace Hl7Converter.Services;

/// <summary>
/// Main service for HL7 message enhancement workflow
/// </summary>
public class Hl7EnhancementService
{
    private readonly PatientDataService _patientDataService;
    private readonly FileStorageService _fileStorageService;
    private readonly Hl7MessageEnhancer _messageEnhancer;
    private readonly ILogger<Hl7EnhancementService> _logger;
    private readonly Hl7EnhancementConfig _config;

    public Hl7EnhancementService(
        PatientDataService patientDataService,
        FileStorageService fileStorageService,
        Hl7MessageEnhancer messageEnhancer,
        ILogger<Hl7EnhancementService> logger,
        Hl7EnhancementConfig config)
    {
        _patientDataService = patientDataService;
        _fileStorageService = fileStorageService;
        _messageEnhancer = messageEnhancer;
        _logger = logger;
        _config = config;
    }

    /// <summary>
    /// Processes an HL7 message through the complete enhancement workflow
    /// </summary>
    /// <param name="filename">Original filename</param>
    /// <param name="hl7Content">HL7 message content</param>
    /// <returns>Processing result with status and file paths</returns>
    public async Task<Hl7ProcessingResult> ProcessMessageAsync(string filename, string hl7Content)
    {
        var result = new Hl7ProcessingResult
        {
            Filename = filename,
            ProcessingStartTime = DateTime.UtcNow
        };

        try
        {
            _logger.LogInformation("Starting HL7 message processing for file: {Filename}", filename);

            // Step 1: Save original message
            result.OriginalFilePath = await _fileStorageService.SaveOriginalMessageAsync(filename, hl7Content);

            // Step 2: Parse HL7 message
            var message = new Message(hl7Content);
            var isParsed = message.ParseMessage();

            if (!isParsed)
            {
                result.Status = "Failed";
                result.ErrorMessage = "Failed to parse HL7 message";
                _logger.LogError("Failed to parse HL7 message for file: {Filename}", filename);
                return result;
            }

            // Step 3: Extract MRN from PID.3 segment
            var mrn = ExtractMrnFromMessage(message);
            if (string.IsNullOrEmpty(mrn))
            {
                result.Status = "Failed";
                result.ErrorMessage = "Could not extract MRN from PID.3 segment";
                _logger.LogError("Could not extract MRN from PID.3 segment for file: {Filename}", filename);
                return result;
            }

            result.ExtractedMrn = mrn;
            _logger.LogInformation("Extracted MRN: {MRN} from file: {Filename}", mrn, filename);

            // Step 4: Extract current fields from message
            var extractedFields = ExtractFieldsFromMessage(message);
            result.ExtractedFields = extractedFields;

            // Step 5: Query database for patient data (if enabled)
            string enhancedContent = hl7Content; // Default to original content

            if (_config.EnableDatabaseLookup)
            {
                var patient = await _patientDataService.GetPatientByHealthCardNumberAsync(mrn);
                
                if (patient != null)
                {
                    _logger.LogInformation("Patient data found for MRN: {MRN}", mrn);
                    
                    // Step 6: Enhance message with missing fields
                    enhancedContent = _messageEnhancer.EnhanceMessage(hl7Content, patient, extractedFields);
                    result.WasEnhanced = !string.Equals(hl7Content, enhancedContent, StringComparison.Ordinal);
                }
                else
                {
                    _logger.LogWarning("Patient not found for MRN: {MRN}. Returning original message.", mrn);
                }
            }
            else
            {
                _logger.LogInformation("Database lookup disabled. Returning original message.");
            }

            // Step 7: Save enhanced message
            result.EnhancedFilePath = await _fileStorageService.SaveEnhancedMessageAsync(filename, enhancedContent);

            result.Status = "Success";
            result.ProcessingEndTime = DateTime.UtcNow;
            result.ProcessingDuration = result.ProcessingEndTime.Value - result.ProcessingStartTime;

            _logger.LogInformation("HL7 message processing completed successfully for file: {Filename}. Enhanced: {WasEnhanced}", 
                filename, result.WasEnhanced);

            return result;
        }
        catch (Exception ex)
        {
            result.Status = "Failed";
            result.ErrorMessage = ex.Message;
            result.ProcessingEndTime = DateTime.UtcNow;
            result.ProcessingDuration = result.ProcessingEndTime.Value - result.ProcessingStartTime;

            _logger.LogError(ex, "Error processing HL7 message for file: {Filename}", filename);
            return result;
        }
    }

    /// <summary>
    /// Extracts MRN (Health Card Number) from PID.3 segment
    /// </summary>
    private string? ExtractMrnFromMessage(Message message)
    {
        try
        {
            // PID.3 contains patient identifiers, look for MRN
            var patientId = message.GetValue("PID.3");
            
            if (!string.IsNullOrEmpty(patientId))
            {
                // Handle multiple identifiers separated by ~
                var identifiers = patientId.Split('~');
                
                foreach (var identifier in identifiers)
                {
                    var parts = identifier.Split('^');
                    if (parts.Length >= 5 && parts[4]?.Equals("MRN", StringComparison.OrdinalIgnoreCase) == true)
                    {
                        return parts[0]; // Return the ID value
                    }
                }
                
                // If no MRN type found, return the first identifier
                var firstIdParts = identifiers[0].Split('^');
                return firstIdParts[0];
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting MRN from message");
            return null;
        }
    }

    /// <summary>
    /// Extracts fields from HL7 message using existing logic
    /// </summary>
    private Hl7ExtractedFields ExtractFieldsFromMessage(Message message)
    {
        var result = new Hl7ExtractedFields();
        var obxSegments = message.Segments("OBX");
        
        for (var i = 0; i < obxSegments.Count; i++)
        {
            var identifier = (message.GetValue($"OBX({i+1}).3") ?? "").Replace(" ", "").Replace("_", "").ToUpperInvariant();
            var value = message.GetValue($"OBX({i+1}).5");
            
            switch (identifier)
            {
                case "QATARIDEXP":
                    result.QATAR_ID_EXP = value;
                    break;
                case "HCEXPDATE":
                    result.HC_EXP_DATE = value;
                    break;
                case "FAMILYPHYSICIAN":
                    result.FAMILY_PHYSICIAN = value;
                    break;
                case "PRIMORGNAME":
                    result.PRIM_ORG_NAME = value;
                    break;
            }
        }
        
        return result;
    }
}
