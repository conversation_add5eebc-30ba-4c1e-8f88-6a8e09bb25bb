using System.Text.Json.Serialization;
using Efferent.HL7.V2;

namespace Hl7Converter;

public record MshSegment
{
    [JsonPropertyName("fieldSeparator")] public string? FieldSeparator { get; set; }
    [JsonPropertyName("encodingCharacters")] public string? EncodingCharacters { get; set; }
    [JsonPropertyName("sendingApplication")] public string? SendingApplication { get; set; }
    [JsonPropertyName("sendingFacility")] public string? SendingFacility { get; set; }
    [JsonPropertyName("receivingApplication")] public string? ReceivingApplication { get; set; }
    [JsonPropertyName("receivingFacility")] public string? ReceivingFacility { get; set; }
    [JsonPropertyName("dateTimeOfMessage")] public string? DateTimeOfMessage { get; set; }
    public static MshSegment? FromHl7V2(Message? message)
    {
        if (message == null) return null;
        return new MshSegment
        {
            FieldSeparator = message.GetValue("MSH.1"),
            EncodingCharacters = message.GetValue("MSH.2"),
            SendingApplication = message.GetValue("MSH.3"),
            SendingFacility = message.GetValue("MSH.4"),
            ReceivingApplication = message.GetValue("MSH.5"),
            ReceivingFacility = message.GetValue("MSH.6"),
            DateTimeOfMessage = message.GetValue("MSH.7")
        };
    }
}