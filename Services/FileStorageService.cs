using Microsoft.Extensions.Logging;

namespace Hl7Converter.Services;

/// <summary>
/// Service for managing local file storage with timestamped audit trails
/// </summary>
public class FileStorageService
{
    private readonly ILogger<FileStorageService> _logger;
    private readonly string _originalMessagesPath;
    private readonly string _enhancedMessagesPath;

    public FileStorageService(ILogger<FileStorageService> logger, string originalMessagesPath, string enhancedMessagesPath)
    {
        _logger = logger;
        _originalMessagesPath = originalMessagesPath;
        _enhancedMessagesPath = enhancedMessagesPath;
        
        // Ensure directories exist
        EnsureDirectoriesExist();
    }

    /// <summary>
    /// Saves original HL7 message to local storage
    /// </summary>
    /// <param name="filename">Original filename</param>
    /// <param name="hl7Content">HL7 message content</param>
    /// <returns>Full path of saved file</returns>
    public async Task<string> SaveOriginalMessageAsync(string filename, string hl7Content)
    {
        try
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var baseFilename = Path.GetFileNameWithoutExtension(filename);
            var extension = Path.GetExtension(filename);
            
            var timestampedFilename = $"{baseFilename}_{timestamp}_original{extension}";
            var fullPath = Path.Combine(_originalMessagesPath, timestampedFilename);

            await File.WriteAllTextAsync(fullPath, hl7Content);
            
            _logger.LogInformation("Original HL7 message saved: {FilePath}", fullPath);
            return fullPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving original HL7 message: {Filename}", filename);
            throw;
        }
    }

    /// <summary>
    /// Saves enhanced HL7 message to local storage
    /// </summary>
    /// <param name="filename">Original filename</param>
    /// <param name="hl7Content">Enhanced HL7 message content</param>
    /// <returns>Full path of saved file</returns>
    public async Task<string> SaveEnhancedMessageAsync(string filename, string hl7Content)
    {
        try
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var baseFilename = Path.GetFileNameWithoutExtension(filename);
            var extension = Path.GetExtension(filename);
            
            var timestampedFilename = $"{baseFilename}_{timestamp}_enhanced{extension}";
            var fullPath = Path.Combine(_enhancedMessagesPath, timestampedFilename);

            await File.WriteAllTextAsync(fullPath, hl7Content);
            
            _logger.LogInformation("Enhanced HL7 message saved: {FilePath}", fullPath);
            return fullPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving enhanced HL7 message: {Filename}", filename);
            throw;
        }
    }

    /// <summary>
    /// Ensures required directories exist
    /// </summary>
    private void EnsureDirectoriesExist()
    {
        try
        {
            if (!Directory.Exists(_originalMessagesPath))
            {
                Directory.CreateDirectory(_originalMessagesPath);
                _logger.LogInformation("Created directory: {Path}", _originalMessagesPath);
            }

            if (!Directory.Exists(_enhancedMessagesPath))
            {
                Directory.CreateDirectory(_enhancedMessagesPath);
                _logger.LogInformation("Created directory: {Path}", _enhancedMessagesPath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating directories");
            throw;
        }
    }
}
