using System.Text.Json.Serialization;

namespace Hl7Converter;

public record Hl7ExportJson(
    [property: <PERSON>sonPropertyName("exportMetadata")] ExportMetadata ExportMetadata,
    [property: JsonPropertyName("messages")] List<Hl7MessageJson> Messages,
    [property: Json<PERSON>ropertyName("processingStatistics")] ProcessingStatistics ProcessingStatistics
)
{
    public Hl7ExportJson() : this(new ExportMetadata(), new List<Hl7MessageJson>(), new ProcessingStatistics()) { }
}