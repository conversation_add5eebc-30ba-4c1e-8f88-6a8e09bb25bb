using System.Text.Json.Serialization;

namespace Hl7Converter;

public record ExportMetadata
{
    [JsonPropertyName("totalMessages")] public int TotalMessages { get; set; }
    [JsonPropertyName("exportTimestamp")] public string? ExportTimestamp { get; set; }
    [JsonPropertyName("processingTimestamp")] public string? ProcessingTimestamp { get; set; }
    [JsonPropertyName("sourceFile")] public string? SourceFile { get; set; }
    [JsonPropertyName("parsingEngine")] public string? ParsingEngine { get; set; }
    [JsonPropertyName("dotNetVersion")] public string? DotNetVersion { get; set; }
}