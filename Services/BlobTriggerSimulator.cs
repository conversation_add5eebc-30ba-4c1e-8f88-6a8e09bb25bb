using Azure.Storage.Blobs;
using Microsoft.Extensions.Logging;

namespace Hl7Converter.Services;

/// <summary>
/// Simulates Azure Blob trigger functionality for console application
/// </summary>
public class BlobTriggerSimulator
{
    private readonly BlobServiceClient _blobServiceClient;
    private readonly ILogger<BlobTriggerSimulator> _logger;
    private readonly string _containerName;

    public BlobTriggerSimulator(BlobServiceClient blobServiceClient, ILogger<BlobTriggerSimulator> logger, string containerName)
    {
        _blobServiceClient = blobServiceClient;
        _logger = logger;
        _containerName = containerName;
    }

    /// <summary>
    /// Simulates blob trigger by polling for new HL7 files
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Enumerable of blob names and content</returns>
    public async IAsyncEnumerable<(string BlobName, string Content)> SimulateBlobTriggerAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting blob trigger simulation for container: {ContainerName}", _containerName);

            var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
            
            // Check if container exists
            var containerExists = await containerClient.ExistsAsync(cancellationToken);
            if (!containerExists)
            {
                _logger.LogWarning("Container {ContainerName} does not exist", _containerName);
                yield break;
            }

            // List all blobs in the container
            await foreach (var blobItem in containerClient.GetBlobsAsync(cancellationToken: cancellationToken))
            {
                if (cancellationToken.IsCancellationRequested)
                    yield break;

                // Filter for HL7 files (typically .hl7 extension)
                if (IsHl7File(blobItem.Name))
                {
                    try
                    {
                        _logger.LogInformation("Processing blob: {BlobName}", blobItem.Name);

                        var blobClient = containerClient.GetBlobClient(blobItem.Name);
                        var response = await blobClient.DownloadContentAsync(cancellationToken);
                        
                        var content = response.Value.Content.ToString();
                        
                        yield return (blobItem.Name, content);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing blob: {BlobName}", blobItem.Name);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in blob trigger simulation");
        }
    }

    /// <summary>
    /// Simulates processing files from a local directory (for testing without Azure)
    /// </summary>
    /// <param name="directoryPath">Local directory path containing HL7 files</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Enumerable of file names and content</returns>
    public async IAsyncEnumerable<(string FileName, string Content)> SimulateLocalDirectoryAsync(string directoryPath, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting local directory simulation for path: {DirectoryPath}", directoryPath);

            if (!Directory.Exists(directoryPath))
            {
                _logger.LogWarning("Directory {DirectoryPath} does not exist", directoryPath);
                yield break;
            }

            var files = Directory.GetFiles(directoryPath, "*.*", SearchOption.TopDirectoryOnly)
                .Where(file => IsHl7File(Path.GetFileName(file)));

            foreach (var filePath in files)
            {
                if (cancellationToken.IsCancellationRequested)
                    yield break;

                try
                {
                    var fileName = Path.GetFileName(filePath);
                    _logger.LogInformation("Processing file: {FileName}", fileName);

                    var content = await File.ReadAllTextAsync(filePath, cancellationToken);
                    
                    yield return (fileName, content);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing file: {FilePath}", filePath);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in local directory simulation");
        }
    }

    /// <summary>
    /// Determines if a file is an HL7 file based on its name/extension
    /// </summary>
    private static bool IsHl7File(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return extension == ".hl7" || extension == ".txt" || 
               fileName.ToLowerInvariant().Contains("hl7") ||
               fileName.ToLowerInvariant().Contains("adt");
    }
}
