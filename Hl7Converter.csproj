<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="HL7-V2" Version="3.4.0" />
    <PackageReference Include="Markdig" Version="0.41.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.5" />
    <PackageReference Include="System.Text.Json" Version="9.0.5" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="hl7-messages\enhanced\" />
    <Folder Include="hl7-messages\original\" />
  </ItemGroup>

</Project>
