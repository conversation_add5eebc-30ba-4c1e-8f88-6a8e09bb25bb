{"exportMetadata": {"totalMessages": 2, "exportTimestamp": "2025-06-01T21:34:22", "processingTimestamp": "2025-06-01T21:34:22", "sourceFile": "test_specific_fields.md", "parsingEngine": "HL7-V2 3.4.0", "dotNetVersion": ".NET 8.0"}, "messages": [{"messageMetadata": {"messageId": "MSG00001", "messageType": "ADT^A08", "triggerEvent": "A08", "sourceFile": "MDFiles/test_specific_fields.md", "processingStatus": "Success", "validationErrors": [], "segmentCount": 4}, "mshSegment": {"fieldSeparator": "|", "encodingCharacters": "^~\\&", "sendingApplication": "SENDING_APP", "sendingFacility": "SENDING_FACILITY", "receivingApplication": "RECEIVING_APP", "receivingFacility": "RECEIVING_FACILITY", "dateTimeOfMessage": "20250601134258"}, "pidSegment": {"qatarIdExpiration": "QATAR_ID_EXP", "healthCardExpiration": "", "familyPhysician": "PHYSICIAN^FAMILY^DR^^^DR", "primaryOrganizationName": "PRIMARY"}}, {"messageMetadata": {"messageId": "MSG00002", "messageType": "ADT^A31", "triggerEvent": "A31", "sourceFile": "MDFiles/test_specific_fields.md", "processingStatus": "Success", "validationErrors": [], "segmentCount": 4}, "mshSegment": {"fieldSeparator": "|", "encodingCharacters": "^~\\&", "sendingApplication": "SENDING_APP", "sendingFacility": "SENDING_FACILITY", "receivingApplication": "RECEIVING_APP", "receivingFacility": "RECEIVING_FACILITY", "dateTimeOfMessage": "20250601134259"}, "pidSegment": {"qatarIdExpiration": "QATAR_ID_EXP", "healthCardExpiration": "", "familyPhysician": "DOCTOR^PRIMARY^DR^^^DR", "primaryOrganizationName": "MAIN"}}], "processingStatistics": {"totalProcessingTime": "00:00:00.000", "successfulMessages": 2, "failedMessages": 0}}