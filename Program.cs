using System.Text.Json;
using Efferent.HL7.V2;
using Markdig;
using Markdig.Syntax;
using Hl7Converter;

// Entry point
// Hardcoded path for the Markdown file
const string markdownPath = @"C:\\Users\\<USER>\\RiderProjects\\Hl7Converter\\MDFiles\\received_hl7_messages_20250601_140004.md";

if (!File.Exists(markdownPath))
{
    Console.WriteLine($"File not found: {markdownPath}");
    return;
}

var markdown = await File.ReadAllTextAsync(markdownPath);
var pipeline = new MarkdownPipelineBuilder().Build();
var document = Markdown.Parse(markdown, pipeline);

// Extract code blocks containing HL7 messages
var codeBlocks = document.Descendants<CodeBlock>()
    .OfType<FencedCodeBlock>()
    .Where(cb => cb.Info == "hl7" || string.IsNullOrEmpty(cb.Info))
    .ToList();

Console.WriteLine($"Found {codeBlocks.Count} code blocks in the markdown file.");

var messages = new List<Hl7MessageJson>();
var successCount = 0;
var failureCount = 0;

var blockIndex = 0;
foreach (var block in codeBlocks)
{
    blockIndex++;
    ProcessCodeBlock(block, blockIndex, codeBlocks.Count, markdownPath, messages, ref successCount, ref failureCount);
}

Console.WriteLine($"\nSummary: {successCount} messages parsed successfully, {failureCount} failed.");

// --- Minimal Extraction Output Only ---
var extractedList = new List<dynamic>();
blockIndex = 0;
foreach (var block in codeBlocks)
{
    blockIndex++;
    var hl7Raw = block.Lines.ToString();
    try
    {
        var message = new Message(hl7Raw);
        var isParsed = message.ParseMessage();
        if (isParsed)
        {
            var extracted = ExtractFieldsFromMessage(message);
            // Add messageId from MSH.10
            var messageId = message.GetValue("MSH.10");
            extractedList.Add(new {
                messageId,
                extracted.QATAR_ID_EXP,
                extracted.HC_EXP_DATE,
                extracted.FAMILY_PHYSICIAN,
                extracted.PRIM_ORG_NAME
            });
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Error processing message: {ex.Message} {(ex.InnerException != null ? $"Error: {ex.InnerException.Message}" : "")}");
    }
}

var outputPath = Path.ChangeExtension(markdownPath, ".json");
var extractedJson = JsonSerializer.Serialize(extractedList, new JsonSerializerOptions { WriteIndented = true });
await File.WriteAllTextAsync(outputPath, extractedJson);
Console.WriteLine($"Extracted fields saved to {outputPath}");

// --- Helper Methods ---
static Hl7ExtractedFields ExtractFieldsFromMessage(Message message)
{
    var result = new Hl7ExtractedFields();
    var obxSegments = message.Segments("OBX");
    for (var i = 0; i < obxSegments.Count; i++)
    {
        var identifier = (message.GetValue($"OBX({i+1}).3") ?? "").Replace(" ", "").Replace("_", "").ToUpperInvariant();
        var value = message.GetValue($"OBX({i+1}).5");
        switch (identifier)
        {
            case "QATARIDEXP":
                result.QATAR_ID_EXP = value;
                break;
            case "HCEXPDATE":
                result.HC_EXP_DATE = value;
                break;
            case "FAMILYPHYSICIAN":
                result.FAMILY_PHYSICIAN = value;
                break;
            case "PRIMORGNAME":
                result.PRIM_ORG_NAME = value;
                break;
        }
    }
    return result;
}

static void ProcessCodeBlock(FencedCodeBlock block, int blockIndex, int totalBlocks, string markdownPath, List<Hl7MessageJson> messages, ref int successCount, ref int failureCount)
{
    var hl7Raw = block.Lines.ToString();
    Console.WriteLine($"\nProcessing block {blockIndex}/{totalBlocks}:");
    try
    {
        Console.WriteLine($"Raw content (first 100 chars): {GetFirstChars(hl7Raw, 100)}");
        var message = ParseMessageWithLogging(hl7Raw, out var isParsed);
        Console.WriteLine($"Message parsed successfully: {isParsed}");
        if (isParsed)
        {
            if (TryProcessMessage(message, markdownPath, messages, out var messageType))
            {
                successCount++;
                Console.WriteLine($"Message type {messageType} processed and added to output list.");
            }
            else
            {
                failureCount++;
            }
        }
        else
        {
            failureCount++;
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Error parsing message: {ex.Message} {(ex.InnerException != null ? $"Error: {ex.InnerException.Message}" : "")}");
        failureCount++;
    }
}

static string GetFirstChars(string input, int count) => input.Length > count ? input[..count] : input;

static Message ParseMessageWithLogging(string hl7Raw, out bool isParsed)
{
    var message = new Message(hl7Raw);
    isParsed = message.ParseMessage();
    return message;
}

static bool TryProcessMessage(Message message, string markdownPath, List<Hl7MessageJson> messages, out string messageType)
{
    messageType = string.Empty;
    try
    {
        messageType = message.GetValue("MSH.9");
        var msg = Hl7MessageJson.FromHl7V2(message, markdownPath);
        if (msg != null)
            messages.Add(msg);
        return true;
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Error processing message: {ex.Message} {(ex.InnerException != null ? $"Error: {ex.InnerException.Message}" : "")}");
        return false;
    }
}